// Client-side API functions that use Next.js API routes as proxy
import { BlogPost, Video, Testimonial, Client, Reel } from './api';

// API response wrapper
interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

// Generic fetch function for client-side API calls
async function fetchFromAPI<T>(endpoint: string): Promise<T[]> {
  try {
    const response = await fetch(`/api${endpoint}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      console.error(
        `Failed to fetch ${endpoint}:`,
        response.status,
        response.statusText
      );
      return [];
    }

    const result: ApiResponse<T[]> = await response.json();

    if (!result.success) {
      console.error(`API error for ${endpoint}:`, result.message);
      return [];
    }

    return result.data || [];
  } catch (error) {
    console.error(`Network error fetching ${endpoint}:`, error);
    return [];
  }
}

// Client-side API functions
export async function getClientBlogPosts(): Promise<BlogPost[]> {
  const posts = await fetchFromAPI<BlogPost>("/blog");
  return posts
    .filter((post) => post.status === "published")
    .sort(
      (a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );
}

export async function getClientVideos(): Promise<Video[]> {
  const videos = await fetchFromAPI<Video>("/videos");
  return videos
    .filter((video) => video.status === "published")
    .sort((a, b) => a.order - b.order);
}

export async function getClientTestimonials(): Promise<Testimonial[]> {
  const testimonials = await fetchFromAPI<Testimonial>("/testimonials");
  return testimonials
    .filter((testimonial) => testimonial.status === "published")
    .sort((a, b) => a.order - b.order);
}

export async function getClientClients(): Promise<Client[]> {
  const clients = await fetchFromAPI<Client>("/clients");
  return clients
    .filter((client) => client.status === "published")
    .sort((a, b) => a.order - b.order);
}

export async function getClientReels(): Promise<Reel[]> {
  const reels = await fetchFromAPI<Reel>("/reels");
  return reels
    .filter((reel) => reel.status === "published")
    .sort((a, b) => a.order - b.order);
}

// Helper functions to get featured items
export async function getClientFeaturedBlogPosts(): Promise<BlogPost[]> {
  const posts = await getClientBlogPosts();
  return posts.filter((post) => post.featured);
}

export async function getClientFeaturedVideos(): Promise<Video[]> {
  const videos = await getClientVideos();
  return videos.filter((video) => video.featured);
}

export async function getClientFeaturedTestimonials(): Promise<Testimonial[]> {
  const testimonials = await getClientTestimonials();
  return testimonials.filter((testimonial) => testimonial.featured);
}

export async function getClientFeaturedClients(): Promise<Client[]> {
  const clients = await getClientClients();
  return clients.filter((client) => client.featured);
}

export async function getClientFeaturedReels(): Promise<Reel[]> {
  const reels = await getClientReels();
  return reels.filter((reel) => reel.featured);
}
