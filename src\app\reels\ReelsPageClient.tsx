'use client';

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { ArrowLeft, Play, X, Grid, List } from 'lucide-react';
import { Dialog, DialogContent, DialogTrigger, DialogClose } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { type Reel } from '@/lib/api';

interface ReelsPageClientProps {
  allReels: Reel[];
  featuredReels: Reel[];
}

export default function ReelsPageClient({ allReels, featuredReels }: ReelsPageClientProps) {
  const [selectedReel, setSelectedReel] = useState<string | null>(null);
  const [filteredReels] = useState<Reel[]>(allReels);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  // No platform filtering needed - only YouTube Shorts supported

  // Only YouTube Shorts supported now
  const getPlatformIcon = () => <Play size={16} />;
  const getPlatformColor = () => 'bg-accent';

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="bg-gradient-to-br from-primary via-foreground to-primary text-primary-foreground py-20">
        <div className="container mx-auto px-4">
          <Link
            href="/#shorts"
            className="inline-flex items-center gap-2 text-accent hover:text-primary-foreground transition-colors mb-8 font-accent"
          >
            <ArrowLeft size={20} />
            Back to Portfolio
          </Link>

          <h1 className="text-4xl md:text-5xl  text-primary-foreground font-heading font-bold mb-4">
            YouTube Shorts Collection
          </h1>
          <p className="text-xl text-primary-foreground/90 max-w-2xl font-body">
            Discover my creative short-form content on YouTube. Engaging shorts that showcase dynamic editing and storytelling in under 60 seconds.
          </p>
        </div>
      </div>

      <div className="container mx-auto px-4 py-16">
        {/* Featured Shorts */}
        {featuredReels.length > 0 && (
          <section className="mb-16">
            <h2 className="text-3xl font-heading font-bold text-foreground mb-8">
              Featured YouTube Shorts
            </h2>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-6">
              {featuredReels.map((reel) => (
                <Dialog
                  key={reel._id}
                  open={selectedReel === reel.id}
                  onOpenChange={(open) => {
                    if (!open) {
                      setSelectedReel(null);
                    }
                  }}
                >
                  <DialogTrigger asChild>
                    <div
                      onClick={() => setSelectedReel(reel.id)}
                      className="group bg-card rounded-xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 hover:scale-105 cursor-pointer"
                    >
                      <div className="relative aspect-[9/16] overflow-hidden">
                        <Image
                          src={reel.thumbnail || ''}
                          alt={reel.title}
                          fill
                          className="object-cover transition-transform duration-500 group-hover:scale-110"
                        />

                        <div className="absolute inset-0 bg-primary/20 group-hover:bg-primary/40 transition-colors duration-300 flex items-center justify-center">
                          <div className="bg-accent/90 backdrop-blur-sm rounded-full p-3 group-hover:scale-110 transition-transform duration-300">
                            <Play className="h-6 w-6 text-accent-foreground ml-0.5" fill="currentColor" />
                          </div>
                        </div>

                        <div className="absolute top-3 left-3">
                          <Badge variant="default" className="bg-accent text-accent-foreground text-xs font-accent">
                            Featured
                          </Badge>
                        </div>

                        <div className="absolute top-3 right-3">
                          <div className={`${getPlatformColor()} text-primary-foreground p-1.5 rounded-full`}>
                            {getPlatformIcon()}
                          </div>
                        </div>
                      </div>

                      <div className="p-4">
                        <h3 className="text-sm font-heading font-semibold text-primary mb-2 group-hover:text-secondary transition-colors duration-300 line-clamp-2">
                          {reel.title}
                        </h3>

                        {reel.description && (
                          <p className="text-muted-foreground text-xs leading-relaxed line-clamp-2">
                            {reel.description}
                          </p>
                        )}
                      </div>
                    </div>
                  </DialogTrigger>

                  <DialogContent className="max-w-lg w-[95vw] p-0 bg-black border-0">
                    <div className="relative aspect-[9/16] max-h-[85vh] w-full">
                      <DialogClose asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="absolute -top-12 right-0 z-50 text-white hover:text-accent transition-colors h-auto w-auto p-2"
                        >
                          <X size={24} />
                        </Button>
                      </DialogClose>

                      {selectedReel === reel.id && reel.embedUrl && (
                        <iframe
                          src={reel.embedUrl}
                          title={reel.title}
                          className="w-full h-full rounded-lg"
                          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                          allowFullScreen
                        />
                      )}
                    </div>
                  </DialogContent>
                </Dialog>
              ))}
            </div>
          </section>
        )}

        {/* View Controls */}
        <div className="flex justify-between items-center mb-8">
          <div className="flex items-center gap-2">
            <Play size={20} className="text-red-600" />
            <span className="font-medium text-primary">YouTube Shorts</span>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('grid')}
            >
              <Grid size={16} />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('list')}
            >
              <List size={16} />
            </Button>
          </div>
        </div>

        {/* All Reels */}
        <section>
          <h2 className="text-3xl font-heading font-bold text-primary mb-8">
            All YouTube Shorts
            <span className="text-lg font-normal text-muted-foreground ml-2">
              ({filteredReels.length} shorts)
            </span>
          </h2>

          {filteredReels.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-lg text-muted-foreground">No YouTube Shorts available at the moment.</p>
            </div>
          ) : (
            <div className={viewMode === 'grid'
              ? "grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-6"
              : "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
            }>
              {filteredReels.map((reel) => (
                <Dialog
                  key={reel._id}
                  open={selectedReel === reel.id}
                  onOpenChange={(open) => {
                    if (!open) {
                      setSelectedReel(null);
                    }
                  }}
                >
                  <DialogTrigger asChild>
                    <div
                      onClick={() => setSelectedReel(reel.id)}
                      className={`group bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 hover:scale-105 cursor-pointer ${viewMode === 'list' ? 'flex' : ''
                        }`}
                    >
                      <div className={`relative overflow-hidden ${viewMode === 'list' ? 'w-32 aspect-[9/16]' : 'aspect-[9/16]'
                        }`}>
                        <Image
                          src={reel.thumbnail || ''}
                          alt={reel.title}
                          fill
                          className="object-cover transition-transform duration-500 group-hover:scale-110"
                        />

                        <div className="absolute inset-0 bg-primary/20 group-hover:bg-primary/40 transition-colors duration-300 flex items-center justify-center">
                          <div className="bg-card/90 backdrop-blur-sm rounded-full p-3 group-hover:scale-110 transition-transform duration-300">
                            <Play className="h-6 w-6 text-primary ml-0.5" fill="currentColor" />
                          </div>
                        </div>

                        {reel.featured && (
                          <div className="absolute top-3 left-3">
                            <Badge variant="secondary" className="text-xs">
                              Featured
                            </Badge>
                          </div>
                        )}

                        <div className="absolute top-3 right-3">
                          <div className={`${getPlatformColor()} text-primary-foreground p-1.5 rounded-full`}>
                            {getPlatformIcon()}
                          </div>
                        </div>
                      </div>

                      <div className="p-4 flex-1">
                        <h3 className={`font-heading font-semibold text-primary mb-2 group-hover:text-secondary transition-colors duration-300 ${viewMode === 'list' ? 'text-base line-clamp-2' : 'text-sm line-clamp-2'
                          }`}>
                          {reel.title}
                        </h3>

                        {reel.description && (
                          <p className={`text-muted-foreground leading-relaxed ${viewMode === 'list' ? 'text-sm line-clamp-3' : 'text-xs line-clamp-2'
                            }`}>
                            {reel.description}
                          </p>
                        )}

                        {viewMode === 'list' && (
                          <div className="mt-3">
                            <Badge
                              variant="outline"
                              className={`text-xs ${getPlatformColor()} text-white border-none`}
                            >
                              <span className="flex items-center gap-1">
                                {getPlatformIcon()}
                                YouTube Shorts
                              </span>
                            </Badge>
                          </div>
                        )}
                      </div>
                    </div>
                  </DialogTrigger>

                  <DialogContent className="max-w-lg w-[95vw] p-0 bg-black border-0">
                    <div className="relative aspect-[9/16] max-h-[85vh] w-full">
                      <DialogClose asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="absolute -top-12 right-0 z-50 text-white hover:text-accent transition-colors h-auto w-auto p-2"
                        >
                          <X size={24} />
                        </Button>
                      </DialogClose>

                      {selectedReel === reel.id && reel.embedUrl && (
                        <iframe
                          src={reel.embedUrl}
                          title={reel.title}
                          className="w-full h-full rounded-lg"
                          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                          allowFullScreen
                        />
                      )}
                    </div>
                  </DialogContent>
                </Dialog>
              ))}
            </div>
          )}
        </section>
      </div>
    </div>
  );
}
